{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "ts-node-dev --poll --files src/index.ts index.d.ts", "start": "node index.js", "build": "rimraf dist && tsc"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.2.0", "resend": "^3.2.0", "zod": "^3.23.5"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.6", "rimraf": "^5.0.7", "ts-node-dev": "^2.0.0", "typescript": "^5.4.5"}}