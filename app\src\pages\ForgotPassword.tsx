import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { Link } from "react-router-dom";
import { sendPasswordResetEmail } from "../lib/api";

const ForgotPassword = () => {
  const [email, setEmail] = useState("");

  const {
    mutate: sendPasswordReset,
    isPending,
    isSuccess,
    isError,
    error,
  } = useMutation({
    mutationFn: sendPasswordResetEmail,
  });

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="w-full max-w-md px-6 py-8 mx-auto space-y-6">
        <h1 className="text-3xl font-bold">Reset your password</h1>
        
        
      </div>
    </div>
  );
};
export default ForgotPassword;
