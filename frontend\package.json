{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@chakra-ui/react": "2.8.2", "@emotion/react": "11.11.4", "@emotion/styled": "11.11.5", "@tanstack/react-query": "5.36.0", "@tanstack/react-query-devtools": "5.36.0", "axios": "1.6.8", "framer-motion": "11.1.9", "react": "18.2.0", "react-dom": "18.2.0", "react-router-dom": "6.23.1"}, "devDependencies": {"@types/react": "18.2.66", "@types/react-dom": "18.2.22", "@vitejs/plugin-react": "4.2.1", "eslint": "8.57.0", "eslint-plugin-react": "7.34.1", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-refresh": "0.4.6", "vite": "5.2.0"}}