name: mern-app

services:
  mongo:
    image: mongo:7.0.12
    volumes:
      - mongo_data:/data/db
    networks:
      - mern-app

  api:
    build:
      context: ./backend
      target: production
    restart: unless-stopped
    env_file:
      - ./backend/.env
    networks:
      - mern-app
    depends_on:
      - mongo

  frontend:
    build:
      context: ./frontend
      target: production
      args:
        VITE_API_URL: https://api.${DOMAIN_NAME}
    restart: unless-stopped
    networks:
      - mern-app
    depends_on:
      - api

  caddy:
    build:
      context: ./caddy
      dockerfile: Dockerfile
    ports:
      - 80:80
      - 443:443
    environment:
      DOMAIN_NAME: ${DOMAIN_NAME}
    volumes:
      - caddy_data:/data
      - caddy_config:/config
    networks:
      - mern-app
    depends_on:
      - frontend
      - api

volumes:
  mongo_data:
  caddy_data:
  caddy_config:

networks:
  mern-app:
    driver: bridge
