{"info": {"_postman_id": "0e1b66ed-dd5f-448a-ba58-3adf22ab8d6c", "name": "Node Auth API", "description": "Auth endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "15039844"}, "item": [{"name": "auth", "item": [{"name": "register", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"123123\",\n    \"confirmPassword\": \"123123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}}, "response": []}, {"name": "login", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"123123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "response": []}, {"name": "refresh", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/refresh", "host": ["{{base_url}}"], "path": ["auth", "refresh"]}}, "response": []}, {"name": "logout", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}, "response": []}, {"name": "verify email", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/email/verify/:code", "host": ["{{base_url}}"], "path": ["auth", "email", "verify", ":code"], "variable": [{"key": "code", "value": "662c74c2bd74a912f8c41baf"}]}}, "response": []}, {"name": "forgot password", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/password/forgot", "host": ["{{base_url}}"], "path": ["auth", "password", "forgot"]}}, "response": []}, {"name": "reset password", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"verificationCode\": \"662c76881161a09656b81a8c\",\n    \"password\": \"111111\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/password/reset", "host": ["{{base_url}}"], "path": ["auth", "password", "reset"]}}, "response": []}], "description": "Contains all authentication related endpoints."}, {"name": "user", "item": [{"name": "user", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/user", "host": ["{{base_url}}"], "path": ["user"]}}, "response": []}], "description": "Contains all user related endpoints."}, {"name": "session", "item": [{"name": "sessions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/sessions", "host": ["{{base_url}}"], "path": ["sessions"]}}, "response": []}, {"name": "sessions/:id", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/sessions/:id", "host": ["{{base_url}}"], "path": ["sessions", ":id"], "variable": [{"key": "id", "value": "662c76ab1161a09656b81a94"}]}}, "response": []}], "description": "Contains all session related endpoints."}, {"name": "health check", "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}}, "request": {"method": "GET", "header": [{"key": "ngrok-skip-browser-warning", "value": "true", "type": "text", "disabled": true}], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "response": []}], "auth": {"type": "bearer"}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:4004", "type": "string"}]}