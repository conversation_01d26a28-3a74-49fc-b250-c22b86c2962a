import { useQuery } from "@tanstack/react-query";
import { usePara<PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { verifyEmail } from "../lib/api";

const VerifyEmail = () => {
  const { code } = useParams();
  const { isPending, isSuccess, isError } = useQuery({
    queryKey: ["emailVerification", code],
    queryFn: () => verifyEmail(code),
  });

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="w-full max-w-md px-6 py-8 mx-auto space-y-6">
        {isPending ? (
          <span>Loading...</span>
        ) : (
          <div className="flex items-center justify-center">
            <div className="w-full max-w-md px-6 py-8 mx-auto space-y-6">
            
              {isSuccess ? "Email Verified!" : "Invalid Link"}
            </div>
            {isError && (
              <p className="text-gray-400">
                The link is either invalid or expired.{" "}
                <Link to="/password/forgot" replace>
                  Get a new link
                </Link>
              </p>
            )}
            <Link to={"/"} replace>
              Back to home
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};
export default VerifyEmail;
