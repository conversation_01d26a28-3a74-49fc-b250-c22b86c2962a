import { useEffect } from "react";
import useAuth from "../hooks/useAuth";

const Profile = () => {
  const { user } = useAuth();
  const { email, verified, createdAt } : any = user || {};

  useEffect(() => {
    console.log(email, verified, createdAt);
  }, [email, verified, createdAt]);
  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="w-full max-w-md px-6 py-8 mx-auto space-y-6">
        <h1 className="text-3xl font-bold">My Account</h1>
        {!verified && (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
            Please verify your email
          </div>
        )}
        <div className="space-y-4">
          <p className="text-gray-500">
            Email:{" "}
            <span className="text-gray-300">
              {email}
            </span>
          </p>
          <p className="text-gray-500">
            Created on{" "}
            <span className="text-gray-300">
              {createdAt && new Date(createdAt).toLocaleDateString("en-US")}
            </span>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Profile;
