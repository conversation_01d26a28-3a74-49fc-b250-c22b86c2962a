import { Route, Routes } from "react-router-dom";

import Profile from "./pages/Profile";
import Login from "./pages/Login";
import Register from "./pages/Register";
import Settings from "./pages/Settings";
import ForgotPassword from "./pages/ForgotPassword";
import ResetPassword from "./pages/ResetPassword";
import VerifyEmail from "./pages/VerifyEmail";
import Home from "./pages/Home";

function App() {
  return (
    <main className="flex flex-col items-center justify-center min-h-screen space-y-20">
      <Routes>
        {/* <Route path="/" element={<Home />}>
          <Route index element={<Profile />} />
          <Route path="settings" element={<Settings />} />
        </Route> */}
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="/profile" element={<Profile />} />
        <Route path="/email/verify/:code" element={<VerifyEmail />} />
        {/* <Route path="/password/forgot" element={<ForgotPassword />} />
        <Route path="/password/reset" element={<ResetPassword />} /> */}
      </Routes>
    </main>
  );
}

export default App;
