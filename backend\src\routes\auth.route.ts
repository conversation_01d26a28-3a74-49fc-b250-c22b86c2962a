import { Router } from "express";
import {
  sendPassword<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  login<PERSON><PERSON><PERSON>,
  logout<PERSON><PERSON><PERSON>,
  refresh<PERSON><PERSON><PERSON>,
  register<PERSON>and<PERSON>,
  resetPassword<PERSON>and<PERSON>,
  verifyEmail<PERSON>andler,
} from "../controllers/auth.controller";

const authRoutes = Router();

// prefix: /auth
authRoutes.post("/register", registerHandler);
authRoutes.post("/login", loginHandler);
authRoutes.get("/refresh", refreshHandler);
authRoutes.get("/logout", logoutHandler);
authRoutes.get("/email/verify/:code", verifyEmailHandler);
authRoutes.post("/password/forgot", sendPasswordResetHandler);
authRoutes.post("/password/reset", resetPasswordHandler);

export default authRoutes;
