import useSessions from "@/hooks/useSessions";
import SessionCard from "../components/SessionCard";

const Settings = () => {
  const { sessions, isPending, isSuccess, isError } : any = useSessions();
  return (
    <div className="flex min-h-screen items-center justify-center">
      <h2 className="text-3xl font-bold">Sessions</h2>
      {isPending && <span>Loading...</span>}
      {isError && <p className="text-red-500">Failed to get sessions.</p>}
      {isSuccess && (
        <div className="space-y-4">
          {sessions?.map((session : any) => (
            <SessionCard key={session._id} session={session} />
          ))}
        </div>
      )}
    </div>
  );
};
export default Settings;
